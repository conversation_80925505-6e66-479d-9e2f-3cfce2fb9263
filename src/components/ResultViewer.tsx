"use client";

import type { <PERSON><PERSON><PERSON>, Paragraph } from "@/app/api/recognize/route";
import React, { useState, useEffect } from "react";
import { X, ChevronLeft, ChevronRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import {
  Card,
  CardAction,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

interface ResultViewerProps {
  analysis: Script | null;
  onAnalysisUpdate?: (updatedAnalysis: Script) => void;
}

export default function ResultViewer({
  analysis,
  onAnalysisUpdate,
}: ResultViewerProps) {
  const [scripts, setScripts] = useState<Paragraph[]>(
    analysis?.paragraphs || []
  );

  // Update local state when analysis changes
  useEffect(() => {
    setScripts(analysis?.paragraphs || []);
  }, [analysis]);

  if (!analysis) return null;

  // Function to wrap text at 24 characters
  const wrapText = (text: string, maxLength: number = 24): string => {
    if (text.length <= maxLength) return text;

    const lines = [];
    for (let i = 0; i < text.length; i += maxLength) {
      lines.push(text.slice(i, i + maxLength));
    }
    return lines.join("\n");
  };

  const moveRight = (index: number) => {
    if (index === 0) return; // Already at the leftmost position

    const newScripts = [...scripts];
    const temp = newScripts[index];
    newScripts[index] = newScripts[index - 1];
    newScripts[index - 1] = temp;

    setScripts(newScripts);

    // Notify parent component of the change
    if (onAnalysisUpdate) {
      onAnalysisUpdate({ paragraphs: newScripts });
    }
  };

  const moveLeft = (index: number) => {
    if (index === scripts.length - 1) return; // Already at the rightmost position

    const newScripts = [...scripts];
    const temp = newScripts[index];
    newScripts[index] = newScripts[index + 1];
    newScripts[index + 1] = temp;

    setScripts(newScripts);

    // Notify parent component of the change
    if (onAnalysisUpdate) {
      onAnalysisUpdate({ paragraphs: newScripts });
    }
  };

  const handleDelete = (index: number) => {
    const newScripts = scripts.filter((_, i) => i !== index);
    setScripts(newScripts);

    // Notify parent component of the change
    if (onAnalysisUpdate) {
      onAnalysisUpdate({ paragraphs: newScripts });
    }
  };

  const handleLinesChange = (index: number, newLines: string) => {
    // Remove existing line breaks and wrap at 24 characters
    const cleanText = newLines.replace(/\n/g, "");
    const wrappedText = wrapText(cleanText);

    const newScripts = [...scripts];
    newScripts[index] = { ...newScripts[index], content: wrappedText };
    setScripts(newScripts);

    // Notify parent component of the change
    if (onAnalysisUpdate) {
      onAnalysisUpdate({ paragraphs: newScripts });
    }
  };

  const handleTypeChange = (
    index: number,
    newType: "lines" | "lyrics" | "directions"
  ) => {
    const newScripts = [...scripts];
    newScripts[index] = { ...newScripts[index], type: newType };
    setScripts(newScripts);

    // Notify parent component of the change
    if (onAnalysisUpdate) {
      onAnalysisUpdate({ paragraphs: newScripts });
    }
  };

  const getTypeStyles = (type: string) => {
    switch (type) {
      case "lines":
        return {
          cardClass:
            "border-blue-300/70 bg-gradient-to-br from-blue-50 via-blue-25 to-white",
          headerClass:
            "bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent",
        };
      case "lyrics":
        return {
          cardClass:
            "border-purple-300/70 bg-gradient-to-br from-purple-50 via-purple-25 to-white",
          headerClass:
            "bg-gradient-to-r from-purple-600 to-purple-700 bg-clip-text text-transparent",
        };
      case "directions":
        return {
          cardClass:
            "border-green-300/70 bg-gradient-to-br from-green-50 via-green-25 to-white",
          headerClass:
            "bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent",
        };
      default:
        return {
          cardClass:
            "border-gray-300/70 bg-gradient-to-br from-gray-50 via-gray-25 to-white",
          headerClass:
            "bg-gradient-to-r from-gray-600 to-gray-700 bg-clip-text text-transparent",
        };
    }
  };

  return (
    <div className="bg-gradient-to-br from-slate-50 to-gray-100 p-8 rounded-xl shadow-inner space-y-6">
      <div>
        <h3 className="font-bold text-xl text-gray-800 mb-2">台本</h3>
        <p className="text-sm text-gray-600 mb-6">
          左右ボタンで並び替え、テキストをクリックして編集
        </p>
        <ScrollArea className="rounded-xl border-2 border-gray-200/50 bg-gradient-to-r from-white to-gray-50 shadow-lg whitespace-nowrap">
          <div className="flex flex-row-reverse w-max space-x-6 space-x-reverse p-6 bg-transparent">
            {scripts.map((script, index) => {
              const typeStyles = getTypeStyles(script.type);
              return (
                <Card
                  key={index}
                  className={`relative border-2 ${typeStyles.cardClass} hover:shadow-2xl rounded-2xl shadow-lg transition-all duration-300 group flex-shrink-0 hover:scale-[1.02] hover:-translate-y-1 backdrop-blur-sm`}
                  style={{
                    width: "auto",
                    minWidth: "140px",
                    maxWidth: "200px",
                  }}
                >
                  <CardHeader className="pb-4 h-32">
                    <CardTitle
                      className={`font-bold text-base tracking-wide ${typeStyles.headerClass} mr-2 mt-4`}
                      style={{
                        writingMode: "vertical-rl",
                        textOrientation: "upright",
                        direction: "ltr",
                      }}
                    >
                      {script.header}
                    </CardTitle>
                    <CardAction>
                      {/* Move left button */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => moveLeft(index)}
                        disabled={index === scripts.length - 1}
                        className="absolute top-3 left-3 opacity-0 group-hover:opacity-100 transition-all duration-200 w-8 h-8 p-0 bg-blue-50 hover:bg-blue-100 border-blue-200 hover:border-blue-300 hover:scale-110 shadow-sm hover:shadow-md disabled:opacity-30 disabled:cursor-not-allowed"
                      >
                        <ChevronLeft className="w-4 h-4 text-blue-500 hover:text-blue-600" />
                      </Button>

                      {/* Move right button */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => moveRight(index)}
                        disabled={index === 0}
                        className="absolute top-3 left-12 opacity-0 group-hover:opacity-100 transition-all duration-200 w-8 h-8 p-0 bg-blue-50 hover:bg-blue-100 border-blue-200 hover:border-blue-300 hover:scale-110 shadow-sm hover:shadow-md disabled:opacity-30 disabled:cursor-not-allowed"
                      >
                        <ChevronRight className="w-4 h-4 text-blue-500 hover:text-blue-600" />
                      </Button>

                      {/* Delete button */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(index)}
                        className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-all duration-200 w-8 h-8 p-0 bg-red-50 hover:bg-red-100 border-red-200 hover:border-red-300 hover:scale-110 shadow-sm hover:shadow-md"
                      >
                        <X className="w-4 h-4 text-red-500 hover:text-red-600" />
                      </Button>
                    </CardAction>
                  </CardHeader>
                  <CardContent className="pt-2">
                    <Textarea
                      value={script.content}
                      onChange={(e) => handleLinesChange(index, e.target.value)}
                      // className={`text-gray-700 hover:text-gray-800 text-sm leading-relaxed flex-1 resize-none border-none bg-transparent hover:bg-blue-50/30 focus:bg-blue-50/50 p-4 shadow-none focus-visible:ring-2 focus-visible:ring-blue-300/50 focus-visible:border-none whitespace-pre-line rounded-lg transition-all duration-200 `}
                      cols={24}
                      wrap="hard"
                      style={{
                        resize: "none",
                        writingMode: "vertical-rl",
                        textOrientation: "upright",
                        lineHeight: "1.6",
                      }}
                      placeholder="セリフを入力..."
                    />
                  </CardContent>
                  <CardFooter>
                    {/* Paragraph Type Selector */}
                    <div className=" mt-8">
                      <Select
                        value={script.type}
                        onValueChange={(value) =>
                          handleTypeChange(
                            index,
                            value as "lines" | "lyrics" | "directions"
                          )
                        }
                      >
                        <SelectTrigger className="w-full h-8 text-xs bg-gray-50 hover:bg-gray-100 border-gray-200 focus:border-blue-300 transition-colors">
                          <SelectValue placeholder="段落タイプを選択" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectLabel>段落タイプ</SelectLabel>
                            <SelectItem value="lines">台詞</SelectItem>
                            <SelectItem value="lyrics">歌詞</SelectItem>
                            <SelectItem value="directions">ト書き</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    </div>
                  </CardFooter>
                </Card>
              );
            })}
          </div>
          <ScrollBar
            orientation="horizontal"
            className="h-3 bg-gray-100 rounded-full"
          />
        </ScrollArea>
      </div>
    </div>
  );
}
